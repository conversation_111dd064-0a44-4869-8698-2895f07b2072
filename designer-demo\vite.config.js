import path from 'node:path'
import { defineConfig, mergeConfig } from 'vite'
import { useTinyEngineBaseConfig } from '@opentiny/tiny-engine-vite-config'

export default defineConfig((configEnv) => {
  const baseConfig = useTinyEngineBaseConfig({
    viteConfigEnv: configEnv,
    root: __dirname,
    iconDirs: [path.resolve(__dirname, './node_modules/@opentiny/tiny-engine/assets/')],
    useSourceAlias: true,
    envDir: './env',
    registryPath: './registry.js'
  })

  const customConfig = {
    envDir: './env',
    // define: {
    //   'process.env': { ...process.env }
    // },
    publicDir: path.resolve(__dirname, './public'),
    server: {
      port: 8090
      // host: '0.0.0.0'
    }
    // build: {
    //   sourcemap: true
    // }
  }

  return mergeConfig(baseConfig, customConfig)
})
