{"name": "tiny-engine", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "pnpm run setup && concurrently 'pnpm:serve:backend' 'pnpm:serve:frontend'", "serve:frontend": "pnpm --filter designer-demo dev", "serve:backend": "pnpm --filter @opentiny/tiny-engine-mock dev", "build:plugin": "pnpm --filter @opentiny/tiny-engine-* --filter @opentiny/tiny-engine build", "build:alpha": "pnpm --filter designer-demo build:alpha", "build:prod": "pnpm --filter designer-demo build", "buildComponentSchemas": "node scripts/buildComponentSchemas.js", "lint": "eslint . --ext .js,.mjs,.jsx,.ts,.mts,.tsx,.vue --fix", "format": "prettier --write --list-different **/*{.vue,.js,.mjs,.jsx,.ts,.mts,.tsx,.html,.json}", "prepare": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || husky install", "pub:premajor": "pnpm run build:plugin && pnpm run build:alpha && pnpm lerna version premajor --preid beta --no-push --yes && lerna publish from-package --pre-dist-tag beta --yes", "pub:preminor": "pnpm run build:plugin && pnpm run build:alpha && pnpm lerna version preminor --preid beta --no-push --yes && lerna publish from-package --pre-dist-tag beta --yes", "pub:prepatch": "pnpm run build:plugin && pnpm run build:alpha && pnpm lerna version prepatch --preid beta --no-push --yes && lerna publish from-package --pre-dist-tag beta --yes", "pub:prerelease": "pnpm run build:plugin && pnpm run build:alpha && pnpm lerna version prerelease --preid beta --no-push --yes && lerna publish from-package --pre-dist-tag beta --yes", "setup": "node ./scripts/setup.js", "splitMaterials": "node ./scripts/splitMaterials.mjs", "buildMaterials": "node ./scripts/buildMaterials.mjs", "updateTemplate": "node ./scripts/updateTemplate.mjs"}, "devDependencies": {"@eslint/js": "^8.57.1", "@types/node": "^18.0.0", "@vue/eslint-config-typescript": "^14.4.0", "@vue/tsconfig": "^0.7.0", "chokidar": "^3.5.3", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "eslint": "^9.21.0", "eslint-plugin-vue": "^9.32.0", "fast-glob": "^3.3.2", "fs-extra": "^10.1.0", "globals": "^16.0.0", "husky": "^8.0.0", "lerna": "^7.2.0", "lint-staged": "^13.2.0", "mysql": "^2.18.1", "picocolors": "^1.0.0", "prettier": "^2.7.1", "typescript": "~5.4.5"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16", "pnpm": ">=8"}, "pnpm": {"patchedDependencies": {"@vue/repl@2.9.0": "patches/@<EMAIL>"}}}