{"name": "designer-demo", "private": true, "version": "2.7.0-rc.0", "type": "module", "scripts": {"dev": "cross-env vite", "build:alpha": "cross-env NODE_OPTIONS=--max-old-space-size=10240 vite", "build": "cross-env NODE_OPTIONS=--max-old-space-size=10240 vite build", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@opentiny/tiny-engine": "workspace:^", "@opentiny/tiny-engine-meta-register": "workspace:^", "@opentiny/tiny-engine-utils": "workspace:*", "@opentiny/vue": "~3.20.0", "@opentiny/vue-design-smb": "~3.20.0", "@opentiny/vue-icon": "~3.20.0", "@opentiny/vue-locale": "~3.20.0", "@opentiny/vue-renderless": "~3.20.0", "@opentiny/vue-theme": "~3.20.0", "@vueuse/core": "^9.6.0", "element-plus": "^2.9.10", "mqtt": "^5.13.0", "vue": "^3.4.21", "vue-router": "4"}, "devDependencies": {"@opentiny/tiny-engine-mock": "workspace:^", "@opentiny/tiny-engine-vite-config": "workspace:^", "@vitejs/plugin-vue": "^5.1.2", "cross-env": "^7.0.3", "vite": "^5.4.2", "vitest": "3.0.9"}}