// 根据环境变量动态设置 material 地址
// const getMaterialUrl = () => {
//   // 在生产环境或构建时使用线上地址
//   if (import.meta.env.MODE === 'production' || import.meta.env.PROD) {
//     // return ['http://*************:8090/mock/bundle.json']
//     return ['http://localhost:8090/mock/bundle.json']
//   }
//   // 开发环境使用本地地址
//   return ['http://localhost:8090/mock/bundle.json']
// }

export default {
  id: 'engine.config',
  theme: 'light',
  material: ['http://localhost:8090/mock/bundle.json'],
  scripts: [],
  styles: []
}
